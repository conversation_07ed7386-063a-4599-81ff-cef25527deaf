modbus_write_registers(3)
=========================


NAME
----
modbus_write_registers - write many registers


SYNOPSIS
--------
*int modbus_write_registers(modbus_t *'ctx', int 'addr', int 'nb', const uint16_t *'src');*


DESCRIPTION
-----------
The *modbus_write_registers()* function shall write the content of the _nb_
holding registers from the array _src_ at address _addr_ of the remote device.

The function uses the Modbus function code 0x10 (preset multiple registers).


RETURN VALUE
------------
The function shall return the number of written registers if
successful. Otherwise it shall return -1 and set errno.


SEE ALSO
--------
linkmb:modbus_write_register[3]
linkmb:modbus_read_registers[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
