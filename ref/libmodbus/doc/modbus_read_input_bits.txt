modbus_read_input_bits(3)
=========================


NAME
----
modbus_read_input_bits - read many input bits


SYNOPSIS
--------
*int modbus_read_input_bits(modbus_t *'ctx', int 'addr', int 'nb', uint8_t *'dest');*


DESCRIPTION
-----------
The *modbus_read_input_bits()* function shall read the content of the _nb_ input
bits to the address _addr_ of the remote device.  The result of reading is stored
in _dest_ array as unsigned bytes (8 bits) set to _TRUE_ or _FALSE_.

You must take care to allocate enough memory to store the results in _dest_
(at least _nb_ * sizeof(uint8_t)).

The function uses the Modbus function code 0x02 (read input status).


RETURN VALUE
------------
The function shall return the number of read input status if
successful. Otherwise it shall return -1 and set errno.


ERRORS
------
*EMBMDATA*::
Too many discrete inputs requested


SEE ALSO
--------
linkmb:modbus_read_input_registers[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
