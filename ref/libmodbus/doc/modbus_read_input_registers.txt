modbus_read_input_registers(3)
==============================


NAME
----
modbus_read_input_registers - read many input registers


SYNOPSIS
--------
*int modbus_read_input_registers(modbus_t *'ctx', int 'addr', int 'nb', uint16_t *'dest');*


DESCRIPTION
-----------
The *modbus_read_input_registers()* function shall read the content of the _nb_
input registers to address _addr_ of the remote device. The result of the
reading is stored in _dest_ array as word values (16 bits).

You must take care to allocate enough memory to store the results in _dest_ (at
least _nb_ * sizeof(uint16_t)).

The function uses the Modbus function code 0x04 (read input registers). The
holding registers and input registers have different historical meaning, but
nowadays it's more common to use holding registers only.


RETURN VALUE
------------
The function shall return the number of read input registers if
successful. Otherwise it shall return -1 and set errno.


ERRORS
------
*EMBMDATA*::
Too many bits requested


SEE ALSO
--------
linkmb:modbus_read_input_bits[3]
linkmb:modbus_write_register[3]
linkmb:modbus_write_registers[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
