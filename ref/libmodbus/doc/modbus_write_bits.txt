modbus_write_bits(3)
====================


NAME
----
modbus_write_bits - write many bits


SYNOPSIS
--------
*int modbus_write_bits(modbus_t *'ctx', int 'addr', int 'nb', const uint8_t *'src');*


DESCRIPTION
-----------
The *modbus_write_bits()* function shall write the status of the _nb_ bits
(coils) from _src_ at the address _addr_ of the remote device. The
_src_ array must contains bytes set to `TRUE` or `FALSE`.

The function uses the Modbus function code 0x0F (force multiple coils).


RETURN VALUE
------------
The function shall return the number of written bits if successful. Otherwise it
shall return -1 and set errno.


ERRORS
------
*EMBMDATA*::
Writing too many bits


SEE ALSO
--------
linkmb:modbus_read_bits[3]
linkmb:modbus_write_bit[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
