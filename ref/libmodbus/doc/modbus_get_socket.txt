modbus_get_socket(3)
====================


NAME
----
modbus_get_socket - get the current socket of the context


SYNOPSIS
--------
*int modbus_get_socket(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_get_socket()* function shall return the current socket or file
descriptor of the libmodbus context.


RETURN VALUE
------------
The function returns the current socket or file descriptor of the context if
successful. Otherwise it shall return -1 and set errno.


SEE ALSO
--------
linkmb:modbus_set_socket[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
