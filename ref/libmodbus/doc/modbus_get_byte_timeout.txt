modbus_get_byte_timeout(3)
==========================


NAME
----
modbus_get_byte_timeout - get timeout between bytes


SYNOPSIS
--------
*int modbus_get_byte_timeout(modbus_t *'ctx', uint32_t *'to_sec', uint32_t *'to_usec');*


DESCRIPTION
-----------
The *modbus_get_byte_timeout()* function shall store the timeout interval
between two consecutive bytes of the same message in the _to_sec_ and _to_usec_
arguments.


RETURN VALUE
------------
The function shall return 0 if successful. Otherwise it shall return -1 and set
errno.


EXAMPLE
-------
[source,c]
-------------------
uint32_t to_sec;
uint32_t to_usec;

/* Save original timeout */
modbus_get_byte_timeout(ctx, &to_sec, &to_usec);
-------------------


SEE ALSO
--------
linkmb:modbus_set_byte_timeout[3]
linkmb:modbus_get_response_timeout[3]
linkmb:modbus_set_response_timeout[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
