modbus_set_byte_timeout(3)
==========================


NAME
----
modbus_set_byte_timeout - set timeout between bytes


SYNOPSIS
--------
*void modbus_set_byte_timeout(modbus_t *'ctx', uint32_t 'to_sec', uint32_t 'to_usec');*


DESCRIPTION
-----------
The *modbus_set_byte_timeout()* function shall set the timeout interval between
two consecutive bytes of the same message. The timeout is an upper bound on the
amount of time elapsed before *select()* returns, if the time elapsed is longer
than the defined timeout, an `ETIMEDOUT` error will be raised by the
function waiting for a response.

The value of _to_usec_ argument must be in the range 0 to 999999.

If both _to_sec_ and _to_usec_ are zero, this timeout will not be used at all.
In this case, *modbus_set_response_timeout()* governs the entire handling of the
response, the full confirmation response must be received before expiration of
the response timeout. When a byte timeout is set, the response timeout is only
used to wait for until the first byte of the response.


RETURN VALUE
------------
The function shall return 0 if successful. Otherwise it shall return -1 and set
errno.


ERRORS
------
*EINVAL*::
The argument _ctx_ is NULL or _to_usec_ is larger than 1000000.


SEE ALSO
--------
linkmb:modbus_get_byte_timeout[3]
linkmb:modbus_get_response_timeout[3]
linkmb:modbus_set_response_timeout[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
