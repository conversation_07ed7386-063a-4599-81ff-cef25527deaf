modbus_rtu_get_rts_delay(3)
===========================


NAME
----
modbus_rtu_get_rts_delay - get the current RTS delay in RTU


SYNOPSIS
--------
*int modbus_rtu_get_rts_delay(modbus_t *'ctx');*


DESCRIPTION
-----------

The _modbus_rtu_get_rts_delay()_ function shall get the current Request To Send
delay period of the libmodbus context 'ctx'.

This function can only be used with a context using a RTU backend.


RETURN VALUE
------------
The _modbus_rtu_get_rts_delay()_ function shall return the current RTS delay in
microseconds if successful. Otherwise it shall return -1 and set errno.


ERRORS
------
*EINVAL*::
The libmodbus backend is not RTU.


SEE ALSO
--------
linkmb:modbus_rtu_set_rts_delay[3]


AUTHORS
-------
<PERSON> <<EMAIL>>

The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
