modbus_tcp_accept(3)
====================


NAME
----
modbus_tcp_accept - accept a new connection on a TCP Modbus socket (IPv4)


SYNOPSIS
--------
*int modbus_tcp_accept(modbus_t *'ctx', int *'s);*


DESCRIPTION
-----------
The *modbus_tcp_accept()* function shall extract the first connection on the
queue of pending connections, create a new socket and store it in libmodbus
context given in argument. If available, _accept4()_ with `SOCK_CLOEXEC` will be
called instead of *accept()*.


RETURN VALUE
------------
The function shall return a new socket if successful.
Otherwise it shall return -1 and set errno.


EXAMPLE
-------
For detailed example, see unit-test-server.c source file in tests directory.

[source,c]
-------------------
...

ctx = modbus_new_tcp("127.0.0.1", 502);
s = modbus_tcp_listen(ctx, 1);
modbus_tcp_accept(ctx, &s);

...

close(s)
modbus_free(ctx);
-------------------

SEE ALSO
--------
linkmb:modbus_tcp_pi_accept[3]
linkmb:modbus_tcp_listen[3]
linkmb:modbus_tcp_pi_listen[3]

AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
