modbus_new_tcp_pi(3)
====================


NAME
----
modbus_new_tcp_pi - create a libmodbus context for TCP Protocol Independent


SYNOPSIS
--------
*modbus_t *modbus_new_tcp_pi(const char *'node', const char *'service');*


DESCRIPTION
-----------
The *modbus_new_tcp_pi()* function shall allocate and initialize a modbus_t
structure to communicate with a Modbus TCP IPv4 or IPv6 server.

The _node_ argument specifies the host name or IP address of the host to connect
to, eg. "***********" , "::1" or "server.com". A NULL value can be used to
listen any addresses in server mode.

The _service_ argument is the service name/port number to connect to. To use the
default Modbus port use the string "502". On many Unix systems, it’s
convenient to use a port number greater than or equal to 1024 because it’s not
necessary to have administrator privileges.


RETURN VALUE
------------
The function shall return a pointer to a *modbus_t* structure if
successful. Otherwise it shall return NULL and set errno to one of the values
defined below.


ERRORS
------
*EINVAL*::
The node string is empty or has been truncated. The service string is empty or
has been truncated.

*ENOMEM*::
Out of memory. Possibly, the application hits its memory limit and/or whole
system is running out of memory.


EXAMPLE
-------
[source,c]
-------------------
modbus_t *ctx;

ctx = modbus_new_tcp_pi("::1", "1502");
if (ctx == NULL) {
    fprintf(stderr, "Unable to allocate libmodbus context\n");
    return -1;
}

if (modbus_connect(ctx) == -1) {
    fprintf(stderr, "Connection failed: %s\n", modbus_strerror(errno));
    modbus_free(ctx);
    return -1;
}
-------------------

SEE ALSO
--------
linkmb:modbus_new_tcp[3]
linkmb:modbus_tcp_pi_listen[3]
linkmb:modbus_free[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
