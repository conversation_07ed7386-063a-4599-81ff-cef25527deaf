modbus_read_bits(3)
===================


NAME
----
modbus_read_bits - read many bits


SYNOPSIS
--------
*int modbus_read_bits(modbus_t *'ctx', int 'addr', int 'nb', uint8_t *'dest');*


DESCRIPTION
-----------
The *modbus_read_bits()* function shall read the status of the _nb_ bits (coils)
to the address _addr_ of the remote device. The result of reading is stored in
_dest_ array as unsigned bytes (8 bits) set to `TRUE` or `FALSE`.

You must take care to allocate enough memory to store the results in _dest_
(at least _nb_ * sizeof(uint8_t)).

The function uses the Modbus function code 0x01 (read coil status).


RETURN VALUE
------------
The function shall return the number of read bits if successful. Otherwise it
shall return -1 and set errno.


ERRORS
------
*EMBMDATA*::
Too many bits requested


SEE ALSO
--------
linkmb:modbus_write_bit[3]
linkmb:modbus_write_bits[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON>éphane Raimbault
<<EMAIL>>
