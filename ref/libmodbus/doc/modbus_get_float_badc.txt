modbus_get_float_badc(3)
========================


NAME
----
modbus_get_float_badc - get a float value from 2 registers in BADC byte order


SYNOPSIS
--------
*float modbus_get_float_badc(const uint16_t *'src');*


DESCRIPTION
-----------
The *modbus_get_float_badc()* function shall get a float from 4 bytes with
swapped bytes (BADC instead of ABCD). The _src_ array must be a pointer on two
16 bits values, for example, if the first word is set to 0x2000 and the second
to 0x47F1, the float value will be read as 123456.0.


RETURN VALUE
------------
The function shall return a float.


SEE ALSO
--------
linkmb:modbus_set_float_badc[3]
linkmb:modbus_get_float_abcd[3]
linkmb:modbus_get_float_cdab[3]
linkmb:modbus_get_float_dcba[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
