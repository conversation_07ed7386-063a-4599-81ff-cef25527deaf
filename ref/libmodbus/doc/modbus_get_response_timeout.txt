modbus_get_response_timeout(3)
==============================


NAME
----
modbus_get_response_timeout - get timeout for response


SYNOPSIS
--------
*int modbus_get_response_timeout(modbus_t *'ctx', uint32_t *'to_sec', uint32_t *'to_usec');*


DESCRIPTION
-----------
The *modbus_get_response_timeout()* function shall return the timeout interval
used to wait for a response in the _to_sec_ and _to_usec_ arguments.


RETURN VALUE
------------
The function shall return 0 if successful. Otherwise it shall return -1 and set
errno.


EXAMPLE
-------
[source,c]
-------------------
uint32_t old_response_to_sec;
uint32_t old_response_to_usec;

/* Save original timeout */
modbus_get_response_timeout(ctx, &old_response_to_sec, &old_response_to_usec);

/* Define a new and too short timeout! */
modbus_set_response_timeout(ctx, 0, 0);
-------------------


SEE ALSO
--------
linkmb:modbus_set_response_timeout[3]
linkmb:modbus_get_byte_timeout[3]
linkmb:modbus_set_byte_timeout[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
