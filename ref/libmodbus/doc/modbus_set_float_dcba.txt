modbus_set_float_dcba(3)
========================


NAME
----
modbus_set_float_dcba - set a float value in 2 registers using DCBA byte order


SYNOPSIS
--------
*void modbus_set_float_dcba(float 'f', uint16_t *'dest');*


DESCRIPTION
-----------
The *modbus_set_float_dcba()* function shall set a float to 4 bytes in inverted
Modbus format (DCBA order). The _dest_ array must be pointer on two 16 bits
values to be able to store the full result of the conversion.


RETURN VALUE
------------
There is no return values.


SEE ALSO
--------
linkmb:modbus_get_float_dcba[3]
linkmb:modbus_set_float[3]
linkmb:modbus_get_float[3]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
