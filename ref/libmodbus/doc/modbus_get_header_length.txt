modbus_get_header_length(3)
===========================


NAME
----
modbus_get_header_length - retrieve the current header length


SYNOPSIS
--------
*int modbus_get_header_length(modbus_t *'ctx');*


DESCRIPTION
-----------
The *modbus_get_header_length()* function shall retrieve the current header
length from the backend. This function is convenient to manipulate a message and
so its limited to low-level operations.


RETURN VALUE
------------
The header length as integer value.


SEE ALSO
--------
linkmb:libmodbus[7]


AUTHORS
-------
The libmodbus documentation was written by <PERSON><PERSON><PERSON><PERSON>
<<EMAIL>>
