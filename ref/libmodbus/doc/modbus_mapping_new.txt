modbus_mapping_new(3)
=====================


NAME
----
modbus_mapping_new - allocate four arrays of bits and registers


SYNOPSIS
--------
*modbus_mapping_t* modbus_mapping_new(int 'nb_bits', int 'nb_input_bits', int 'nb_registers', int 'nb_input_registers');*


DESCRIPTION
-----------
The *modbus_mapping_new()* function shall allocate four arrays to store bits,
input bits, registers and inputs registers. The pointers are stored in
modbus_mapping_t structure. All values of the arrays are initialized to zero.

This function is equivalent to a call of the
linkmb:modbus_mapping_new_start_address[3] function with all start addresses to
`0`.

If it isn't necessary to allocate an array for a specific type of data, you can
pass the zero value in argument, the associated pointer will be NULL.

This function is convenient to handle requests in a Modbus server/slave.


RETURN VALUE
------------
The function shall return the new allocated structure if successful. Otherwise
it shall return NULL and set errno.


ERRORS
------
*ENOMEM*::
Not enough memory


EXAMPLE
-------
[source,c]
-------------------
/* The first value of each array is accessible from the 0 address. */
mb_mapping = modbus_mapping_new(BITS_ADDRESS + BITS_NB,
                                INPUT_BITS_ADDRESS + INPUT_BITS_NB,
                                REGISTERS_ADDRESS + REGISTERS_NB,
                                INPUT_REGISTERS_ADDRESS + INPUT_REGISTERS_NB);
if (mb_mapping == NULL) {
    fprintf(stderr, "Failed to allocate the mapping: %s\n",
            modbus_strerror(errno));
    modbus_free(ctx);
    return -1;
}
-------------------

SEE ALSO
--------
linkmb:modbus_mapping_free[3]
linkmb:modbus_mapping_new_start_address[3]


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
