modbus_report_slave_id(3)
=========================


NAME
----
modbus_report_slave_id - returns a description of the controller


SYNOPSIS
--------
*int modbus_report_slave_id(modbus_t *'ctx', int 'max_dest', uint8_t *'dest');*


DESCRIPTION
-----------
The *modbus_report_slave_id()* function shall send a request to the controller
to obtain a description of the controller.

The response stored in _dest_ contains:

* the slave ID, this unique ID is in reality not unique at all so it's not
  possible to depend on it to know how the information are packed in the
  response.
* the run indicator status (0x00 = OFF, 0xFF = ON)
* additional data specific to each controller. For example, libmodbus returns
  the version of the library as a string.

The function writes at most _max_dest_ bytes from the response to _dest_ so
you must ensure that _dest_ is large enough.

RETURN VALUE
------------
The function shall return the number of read data if successful.

If the output was truncated due to the _max_dest_ limit then the return value is
the number of bytes which would have been written to _dest_ if enough space had
been available. Thus, a return value greater than _max_dest_ means that the
response data was truncated.

Otherwise it shall return -1 and set errno.

EXAMPLE
-------
[source,c]
-------------------
uint8_t tab_bytes[MODBUS_MAX_PDU_LENGTH];

...

rc = modbus_report_slave_id(ctx, MODBUS_MAX_PDU_LENGTH, tab_bytes);
if (rc > 1) {
    printf("Run Status Indicator: %s\n", tab_bytes[1] ? "ON" : "OFF");
}
-------------------


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
