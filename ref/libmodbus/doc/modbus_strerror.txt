modbus_strerror(3)
=================


NAME
----
modbus_strerror - return the error message


SYNOPSIS
--------
*const char *modbus_strerror(int 'errnum');*


DESCRIPTION
-----------
The *modbus_strerror()* function shall return a pointer to an error message
string corresponding to the error number specified by the _errnum_ argument.  As
libmodbus defines additional error numbers over and above those defined by the
operating system, applications should use *modbus_strerror()* in preference to
the standard *strerror()* function.


RETURN VALUE
------------
The *modbus_strerror()* function shall return a pointer to an error message
string.


ERRORS
------
No errors are defined.


EXAMPLE
-------
.Display an error message when a Modbus connection cannot be established
[source,c]
-------------------
if (modbus_connect(ctx) == -1) {
    fprintf(stderr, "Connection failed: %s\n", modbus_strerror(errno));
    abort();
}
-------------------

SEE ALSO
--------
linkmb:libmodbus


AUTHORS
-------
The libmodbus documentation was written by Stéphane Raimbault
<<EMAIL>>
